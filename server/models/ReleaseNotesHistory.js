const mongoose = require('mongoose');

const releaseNotesHistorySchema = new mongoose.Schema({
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },

  // Generated content
  content: {
    type: String,
    required: true,
    default: ''
  },

  generatedAt: {
    type: Date,
    default: Date.now
  },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Updated filters structure for version-specific tags
  filters: {
    tags: [{
      type: String
    }],
    labels: [{
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Label'
      },
      name: String
    }],
    states: [{
      type: String
    }],
    includeFeatures: {
      type: Boolean,
      default: true
    },
    includeRequirements: {
      type: Boolean,
      default: true
    },
    groupByFeature: {
      type: Boolean,
      default: true
    }
  },

  // Statistics about the generated release notes
  stats: {
    featuresIncluded: {
      type: Number,
      default: 0
    },
    requirementsIncluded: {
      type: Number,
      default: 0
    },
    versionsIncluded: {
      type: Number,
      default: 0
    },
    tagsUsed: [{
      type: String
    }]
  }
}, {
  timestamps: true
});

// Add indexes for performance
releaseNotesHistorySchema.index({ group: 1, project: 1, generatedAt: -1 });
releaseNotesHistorySchema.index({ generatedAt: -1 });

// Method to generate release notes content from data
releaseNotesHistorySchema.statics.generateContent = function(data, filters) {
  const { features, requirements } = data;
  let content = '';

  // Header
  content += `# ${filters.title || 'Release Notes'}\n\n`;
  content += `Generated on: ${new Date().toLocaleString()}\n\n`;

  // Summary
  const featureCount = features.length;
  const requirementCount = requirements.length;
  const totalVersions = features.reduce((acc, f) => acc + f.versions.length, 0) +
                       requirements.reduce((acc, r) => acc + r.versions.length, 0);

  content += `## Summary\n\n`;
  content += `- **Features**: ${featureCount}\n`;
  content += `- **Requirements**: ${requirementCount}\n`;
  content += `- **Total Versions**: ${totalVersions}\n\n`;

  // Filter information
  if (filters.tags && filters.tags.length > 0) {
    content += `**Release Tags**: ${filters.tags.join(', ')}\n`;
  }
  if (filters.labels && filters.labels.length > 0) {
    content += `**Labels**: ${filters.labels.map(l => l.name || l).join(', ')}\n`;
  }
  if (filters.states && filters.states.length > 0) {
    content += `**States**: ${filters.states.join(', ')}\n`;
  }
  content += '\n';

  // Group by feature if requested
  if (filters.groupByFeature && filters.includeFeatures) {
    content += `## Features\n\n`;

    features.forEach(feature => {
      const currentVersion = feature.versions[feature.versions.length - 1];
      content += `### ${currentVersion.title}\n\n`;
      content += `**State**: ${currentVersion.state}\n`;

      // Show version-specific tags
      if (currentVersion.releaseTags && currentVersion.releaseTags.length > 0) {
        content += `**Tags**: ${currentVersion.releaseTags.map(t => t.tag).join(', ')}\n`;
      }

      if (currentVersion.description) {
        content += `**Description**: ${currentVersion.description}\n`;
      }

      // Find child requirements for this feature
      const childRequirements = requirements.filter(req =>
        req.feature && req.feature._id.toString() === feature._id.toString()
      );

      if (childRequirements.length > 0) {
        content += `\n**Requirements**:\n`;
        childRequirements.forEach(req => {
          const reqVersion = req.versions[req.versions.length - 1];
          content += `- ${reqVersion.title} (${reqVersion.state})`;
          if (reqVersion.releaseTags && reqVersion.releaseTags.length > 0) {
            content += ` [${reqVersion.releaseTags.map(t => t.tag).join(', ')}]`;
          }
          content += '\n';
        });
      }

      content += '\n';
    });

    // Standalone requirements (not associated with features)
    const standaloneRequirements = requirements.filter(req => !req.feature);
    if (standaloneRequirements.length > 0) {
      content += `## Standalone Requirements\n\n`;
      standaloneRequirements.forEach(req => {
        const currentVersion = req.versions[req.versions.length - 1];
        content += `### ${currentVersion.title}\n\n`;
        content += `**State**: ${currentVersion.state}\n`;

        if (currentVersion.releaseTags && currentVersion.releaseTags.length > 0) {
          content += `**Tags**: ${currentVersion.releaseTags.map(t => t.tag).join(', ')}\n`;
        }

        if (currentVersion.description) {
          content += `**Description**: ${currentVersion.description}\n`;
        }

        content += '\n';
      });
    }
  } else {
    // Flat listing
    if (filters.includeFeatures && features.length > 0) {
      content += `## Features\n\n`;
      features.forEach(feature => {
        const currentVersion = feature.versions[feature.versions.length - 1];
        content += `- **${currentVersion.title}** (${currentVersion.state})`;
        if (currentVersion.releaseTags && currentVersion.releaseTags.length > 0) {
          content += ` [${currentVersion.releaseTags.map(t => t.tag).join(', ')}]`;
        }
        content += '\n';
      });
      content += '\n';
    }

    if (filters.includeRequirements && requirements.length > 0) {
      content += `## Requirements\n\n`;
      requirements.forEach(req => {
        const currentVersion = req.versions[req.versions.length - 1];
        content += `- **${currentVersion.title}** (${currentVersion.state})`;
        if (currentVersion.releaseTags && currentVersion.releaseTags.length > 0) {
          content += ` [${currentVersion.releaseTags.map(t => t.tag).join(', ')}]`;
        }
        if (req.feature) {
          content += ` - Feature: ${req.feature.title}`;
        }
        content += '\n';
      });
    }
  }

  return content;
};

module.exports = mongoose.model('ReleaseNotesHistory', releaseNotesHistorySchema);
