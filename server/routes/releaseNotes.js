const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup, addGroupToData } = require('../middleware/groupAuth');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const Project = require('../models/Project');
const Label = require('../models/Label');
const ReleaseNotesHistory = require('../models/ReleaseNotesHistory');

// Debug logging middleware for release notes routes
router.use((req, res, next) => {
  console.log('Release Notes route hit:', req.method, req.path);
  next();
});

// Get available tags and labels for a project (for filter options)
router.get('/projects/:projectId/release-data', auth, addGroupContext, async (req, res) => {
  try {
    // Build base filter for group access
    let baseFilter = { project: req.params.projectId };
    if (!req.isSuperUser) {
      baseFilter.group = req.userGroup._id;
    }

    // Get all features and requirements for this project
    const features = await Feature.find(baseFilter);
    const requirements = await Requirement.find(baseFilter);

    // Extract unique tags from all versions
    const allTags = new Set();

    features.forEach(feature => {
      feature.versions.forEach(version => {
        if (version.releaseTags) {
          version.releaseTags.forEach(tagObj => {
            allTags.add(tagObj.tag);
          });
        }
      });
    });

    requirements.forEach(requirement => {
      requirement.versions.forEach(version => {
        if (version.releaseTags) {
          version.releaseTags.forEach(tagObj => {
            allTags.add(tagObj.tag);
          });
        }
      });
    });

    // Get all labels for this group (labels are group-scoped, not project-scoped)
    let labelFilter = {};
    if (!req.isSuperUser) {
      labelFilter.group = req.userGroup._id;
    }
    const labels = await Label.find(labelFilter);

    res.json({
      tags: Array.from(allTags).sort(),
      labels: labels
    });
  } catch (err) {
    console.error('Error fetching release data options:', err);
    res.status(500).send('Server Error');
  }
});

// Generate release notes for a project with filtering
router.post('/projects/:projectId/release-notes', auth, addGroupContext, async (req, res) => {
  try {
    const { filters, title, save } = req.body;

    if (!filters) {
      return res.status(400).json({ message: 'Filters are required' });
    }

    // Build base filter for group access
    let baseFilter = { project: req.params.projectId };
    if (!req.isSuperUser) {
      baseFilter.group = req.userGroup._id;
    }

    // Get all features and requirements for this project
    let featuresData = [];
    let requirementsData = [];

    if (filters.includeFeatures) {
      featuresData = await Feature.find(baseFilter)
        .populate('createdBy', 'username firstName lastName')
        .populate('versions.releaseTags.addedBy', 'username firstName lastName')
        .populate('versions.labels', 'name color')
        .sort({ createdAt: -1 });
    }

    if (filters.includeRequirements) {
      requirementsData = await Requirement.find(baseFilter)
        .populate('createdBy', 'username firstName lastName')
        .populate('feature', 'title')
        .populate('versions.releaseTags.addedBy', 'username firstName lastName')
        .populate('versions.labels', 'name color')
        .sort({ createdAt: -1 });
    }

    // Apply filters to get matching items
    let filteredFeatures = featuresData;
    let filteredRequirements = requirementsData;

    // Filter by tags (version-specific)
    if (filters.tags && filters.tags.length > 0) {
      filteredFeatures = featuresData.filter(feature => {
        return feature.versions.some(version =>
          version.releaseTags && version.releaseTags.some(tagObj =>
            filters.tags.includes(tagObj.tag)
          )
        );
      });

      filteredRequirements = requirementsData.filter(requirement => {
        return requirement.versions.some(version =>
          version.releaseTags && version.releaseTags.some(tagObj =>
            filters.tags.includes(tagObj.tag)
          )
        );
      });
    }

    // Filter by labels (current version)
    if (filters.labels && filters.labels.length > 0) {
      const labelIds = filters.labels.map(label => label._id || label);

      filteredFeatures = filteredFeatures.filter(feature => {
        const currentVersion = feature.versions[feature.versions.length - 1];
        return currentVersion.labels && currentVersion.labels.some(label =>
          labelIds.includes(label.toString())
        );
      });

      filteredRequirements = filteredRequirements.filter(requirement => {
        const currentVersion = requirement.versions[requirement.versions.length - 1];
        return currentVersion.labels && currentVersion.labels.some(label =>
          labelIds.includes(label.toString())
        );
      });
    }

    // Filter by states (current version)
    if (filters.states && filters.states.length > 0) {
      filteredFeatures = filteredFeatures.filter(feature => {
        const currentVersion = feature.versions[feature.versions.length - 1];
        return filters.states.includes(currentVersion.state);
      });

      filteredRequirements = filteredRequirements.filter(requirement => {
        const currentVersion = requirement.versions[requirement.versions.length - 1];
        return filters.states.includes(currentVersion.state);
      });
    }

    // Generate release notes content
    const releaseData = {
      features: filteredFeatures,
      requirements: filteredRequirements
    };

    const content = ReleaseNotesHistory.generateContent(releaseData, { ...filters, title });

    // Calculate statistics
    const stats = {
      featuresIncluded: filteredFeatures.length,
      requirementsIncluded: filteredRequirements.length,
      versionsIncluded: filteredFeatures.reduce((acc, f) => acc + f.versions.length, 0) +
                       filteredRequirements.reduce((acc, r) => acc + r.versions.length, 0),
      tagsUsed: filters.tags || []
    };

    // Save to history if requested
    if (save && title && title.trim()) {
      const releaseNotesData = addGroupToData(req, {
        project: req.params.projectId,
        title: title.trim(),
        content: content,
        generatedBy: req.user.userId,
        filters: filters,
        stats: stats
      });

      const releaseNotes = new ReleaseNotesHistory(releaseNotesData);
      await releaseNotes.save();
    }

    res.json({
      data: releaseData,
      releaseNotes: content,
      stats: stats
    });
  } catch (err) {
    console.error('Error generating release notes:', err);
    res.status(500).send('Server Error');
  }
});



// Get historical release notes for a project
router.get('/projects/:projectId/release-notes', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    let filter = { project: req.params.projectId };
    
    // Add group filter for non-super users
    if (!req.isSuperUser) {
      filter.group = req.userGroup._id;
    }

    const releaseNotes = await ReleaseNotesHistory.find(filter)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description')
      .sort({ generatedAt: -1 });

    res.json(releaseNotes);
  } catch (err) {
    console.error('Error fetching release notes history:', err);
    res.status(500).send('Server Error');
  }
});

// Get specific release notes data
router.get('/projects/:projectId/release-notes/:releaseNotesId', auth, addGroupContext, async (req, res) => {
  try {
    const releaseNotes = await ReleaseNotesHistory.findById(req.params.releaseNotesId)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description');

    if (!releaseNotes) {
      return res.status(404).json({ message: 'Release notes not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && releaseNotes.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(releaseNotes);
  } catch (err) {
    console.error('Error fetching specific release notes:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
