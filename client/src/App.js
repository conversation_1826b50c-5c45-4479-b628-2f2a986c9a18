import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { GroupProvider } from './contexts/GroupContext';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import MultiTenantRegister from './components/auth/MultiTenantRegister';
import Dashboard from './components/dashboard/Dashboard';
import ProjectView from './components/project/ProjectView';
import RequirementView from './components/requirement/RequirementView';
import FeatureView from './components/feature/FeatureView';
import ReleaseNotesGenerator from './components/release/ReleaseNotesGenerator';
import ThemeDemo from './components/demo/ThemeDemo';
import LogoTestPage from './components/test/LogoTestPage';
import MainLayout from './components/layout/MainLayout';
import socketManager from './utils/socket';
import { cleanProfessionalTheme } from './themes/cleanProfessional';

const PrivateRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  return isAuthenticated ? (
    <MainLayout>
      {children}
    </MainLayout>
  ) : (
    <Navigate to="/login" />
  );
};

const App = () => {
  useEffect(() => {
    // Initialize socket connection
    socketManager.initialize();
  }, []);

  return (
    <ThemeProvider theme={cleanProfessionalTheme}>
      <CssBaseline />
      <AuthProvider>
        <GroupProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<MultiTenantRegister />} />
              <Route path="/register-legacy" element={<Register />} />
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <Dashboard />
                </PrivateRoute>
              }
            />
            <Route
              path="/project/:projectId"
              element={
                <PrivateRoute>
                  <ProjectView />
                </PrivateRoute>
              }
            />
            <Route
              path="/requirement/:requirementId"
              element={
                <PrivateRoute>
                  <RequirementView />
                </PrivateRoute>
              }
            />
            <Route
              path="/feature/:featureId"
              element={
                <PrivateRoute>
                  <FeatureView />
                </PrivateRoute>
              }
            />
            <Route
              path="/project/:projectId/release-notes"
              element={
                <PrivateRoute>
                  <ReleaseNotesGenerator />
                </PrivateRoute>
              }
            />
            <Route path="/theme-demo" element={<ThemeDemo />} />
            <Route path="/logo-test" element={<LogoTestPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </GroupProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;