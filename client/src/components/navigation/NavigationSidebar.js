import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  CircularProgress,
  Tooltip,
  useMediaQuery,
  useTheme,
  Menu,
  MenuItem
} from '@mui/material';
import {
  ExpandMore,
  ChevronRight as ChevronRightIcon,
  Folder as FolderIcon,
  Assignment as AssignmentIcon,
  Task as TaskIcon,
  Person as PersonIcon,
  Public as PublicIcon,
  CheckCircle as ApprovalIcon,
  Group as GroupIcon,
  Extension as FeatureIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const DEFAULT_DRAWER_WIDTH = 300;
const MIN_DRAWER_WIDTH = 250;
const MAX_DRAWER_WIDTH = 500;

// Custom hook for managing sidebar width
const useSidebarWidth = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [width, setWidth] = useState(() => {
    if (typeof window !== 'undefined' && !isMobile) {
      const saved = localStorage.getItem('sidebarWidth');
      return saved ? parseInt(saved, 10) : DEFAULT_DRAWER_WIDTH;
    }
    return DEFAULT_DRAWER_WIDTH;
  });

  const [isResizing, setIsResizing] = useState(false);

  const updateWidth = useCallback((newWidth) => {
    if (isMobile) return; // Disable resize on mobile

    const clampedWidth = Math.max(MIN_DRAWER_WIDTH, Math.min(MAX_DRAWER_WIDTH, newWidth));
    setWidth(clampedWidth);
    localStorage.setItem('sidebarWidth', clampedWidth.toString());

    // Dispatch custom event to notify MainLayout of width change
    window.dispatchEvent(new CustomEvent('sidebarWidthChanged', {
      detail: { width: clampedWidth }
    }));
  }, [isMobile]);

  const autoFitWidth = useCallback(() => {
    if (isMobile) return;

    // Calculate width needed for longest visible text
    // This is a simplified calculation - in a real implementation you might measure actual text
    const estimatedOptimalWidth = Math.min(MAX_DRAWER_WIDTH, 400);
    updateWidth(estimatedOptimalWidth);
  }, [isMobile, updateWidth]);

  const resetWidth = useCallback(() => {
    updateWidth(DEFAULT_DRAWER_WIDTH);
  }, [updateWidth]);

  return {
    width,
    isResizing,
    setIsResizing,
    updateWidth,
    autoFitWidth,
    resetWidth,
    isMobile
  };
};

const NavigationSidebar = () => {
  const navigate = useNavigate();
  const { axios, user } = useAuth();
  const {
    width: drawerWidth,
    isResizing,
    setIsResizing,
    updateWidth,
    autoFitWidth,
    resetWidth,
    isMobile
  } = useSidebarWidth();
  const [expanded, setExpanded] = useState({
    myProjects: true, // Expand "My Projects" by default
    allProjects: false
  });
  const [loading, setLoading] = useState(true);
  const [projectsWithTasks, setProjectsWithTasks] = useState([]);
  const [memberProjects, setMemberProjects] = useState([]);
  const [allOtherProjects, setAllOtherProjects] = useState([]);

  // Context menu for resize handle
  const [contextMenu, setContextMenu] = useState(null);

  // Resize functionality
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const handleMouseMove = useCallback((e) => {
    const deltaX = e.clientX - startXRef.current;
    const newWidth = startWidthRef.current + deltaX;
    updateWidth(newWidth);
  }, [updateWidth]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [setIsResizing, handleMouseMove]);

  const handleMouseDown = useCallback((e) => {
    if (isMobile) return;

    e.preventDefault();
    setIsResizing(true);
    startXRef.current = e.clientX;
    startWidthRef.current = drawerWidth;

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [isMobile, drawerWidth, setIsResizing, handleMouseMove, handleMouseUp]);

  const handleDoubleClick = useCallback(() => {
    if (isMobile) return;
    autoFitWidth();
  }, [isMobile, autoFitWidth]);

  const handleContextMenu = useCallback((e) => {
    if (isMobile) return;

    e.preventDefault();
    setContextMenu(
      contextMenu === null
        ? {
            mouseX: e.clientX + 2,
            mouseY: e.clientY - 6,
          }
        : null,
    );
  }, [isMobile, contextMenu, setContextMenu]);

  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, [setContextMenu]);

  const handleResetWidth = useCallback(() => {
    resetWidth();
    setContextMenu(null);
  }, [resetWidth, setContextMenu]);

  // Cleanup event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [handleMouseMove, handleMouseUp]);

  useEffect(() => {
    if (user) {
      fetchNavigationData();
    }
  }, [user]);

  // Handle optimistic navigation updates
  useEffect(() => {
    const handleNavigationUpdate = (event) => {
      const { type, projectId, userId, taskStatus, featureId, requirementId, newTitle } = event.detail;
      console.log('Navigation update requested:', { type, projectId, userId, taskStatus, featureId, requirementId, newTitle });

      // Get the current user ID
      const currentUserId = user.id || user._id;

      // Handle different types of updates
      if (type === 'featureTitleUpdated') {
        // Update feature title in navigation
        updateFeatureTitleInNavigation(projectId, featureId, newTitle);
      } else if (type === 'requirementTitleUpdated') {
        // Update requirement title in navigation
        updateRequirementTitleInNavigation(projectId, featureId, requirementId, newTitle);
      } else if (type === 'userAdded' && userId === currentUserId) {
        // Handle user being added to requirement/feature
        updateNavigationOptimistically(projectId, 'userAdded');
      } else if (type === 'approvalToggled' && userId === currentUserId) {
        // Handle approval status changes
        updateNavigationOptimistically(projectId, 'approvalChanged');
      } else if (userId === currentUserId) {
        // Only update task-related changes if they affect the current user
        updateNavigationOptimistically(projectId, taskStatus);
      }
    };

    // Listen for custom navigation update events
    window.addEventListener('updateNavigation', handleNavigationUpdate);

    return () => {
      window.removeEventListener('updateNavigation', handleNavigationUpdate);
    };
  }, [user]);

  const updateNavigationOptimistically = async (projectId, taskStatus) => {
    try {
      // Get the current user ID
      const currentUserId = user.id || user._id;

      // Fetch updated data for just this project to check current task status
      const [projectRes, featuresRes, requirementsRes] = await Promise.all([
        axios.get(`/api/projects/${projectId}`),
        axios.get('/api/features'),
        axios.get('/api/requirements?navigation=true')
      ]);

      const project = projectRes.data;
      const allFeatures = featuresRes.data;
      const allRequirements = requirementsRes.data;

      // Check if user has active tasks in this project
      const projectFeatures = allFeatures.filter(feature => {
        const featureProjectId = typeof feature.project === 'object' ? feature.project._id : feature.project;
        return featureProjectId === project._id;
      });

      const projectRequirements = allRequirements.filter(req => {
        const reqProjectId = typeof req.project === 'object' ? req.project._id : req.project;
        return reqProjectId === project._id;
      });

      let hasActiveTasks = false;

      // Check features and their requirements
      for (const feature of projectFeatures) {
        const featureRequirements = projectRequirements.filter(req => {
          const reqFeatureId = typeof req.feature === 'object' ? req.feature._id : req.feature;
          return reqFeatureId === feature._id;
        });

        for (const requirement of featureRequirements) {
          const currentVersion = requirement.versions[requirement.versions.length - 1];
          const userActiveTasks = currentVersion.tasks?.filter(task => {
            const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
            return taskUserId === currentUserId && task.status === 'pending';
          }) || [];

          // Check for pending approvals
          const isApprover = requirement.approvers && requirement.approvers.some(approver => {
            const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
            return approverId === currentUserId;
          });

          const hasApproved = requirement.approvals && requirement.approvals.some(approval => {
            const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
            return approvalUserId === currentUserId && approval.version === requirement.currentVersion;
          });

          const needsApproval = isApprover && !hasApproved;

          if (userActiveTasks.length > 0 || needsApproval) {
            hasActiveTasks = true;
            break;
          }
        }
        if (hasActiveTasks) break;
      }

      // Check top-level requirements - look at ALL versions for tasks
      if (!hasActiveTasks) {
        const topLevelRequirements = projectRequirements.filter(req => !req.feature);
        for (const requirement of topLevelRequirements) {
          // Check tasks across ALL versions, not just the latest
          let userActiveTasks = [];
          requirement.versions.forEach(version => {
            if (version.tasks) {
              const versionTasks = version.tasks.filter(task => {
                const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                return taskUserId === currentUserId && task.status === 'pending';
              });
              userActiveTasks = userActiveTasks.concat(versionTasks);
            }
          });

          // Check for pending approvals
          const isApprover = requirement.approvers && requirement.approvers.some(approver => {
            const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
            return approverId === currentUserId;
          });

          const hasApproved = requirement.approvals && requirement.approvals.some(approval => {
            const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
            return approvalUserId === currentUserId && approval.version === requirement.currentVersion;
          });

          const needsApproval = isApprover && !hasApproved;

          // Debug approval logic
          if (isApprover) {
            console.log('Approval debug for requirement:', requirement._id, {
              currentUserId,
              isApprover,
              hasApproved,
              needsApproval,
              currentVersion: requirement.currentVersion,
              approvers: requirement.approvers?.map(a => ({
                userId: typeof a.user === 'object' ? a.user._id : a.user,
                addedInVersion: a.addedInVersion
              })),
              approvals: requirement.approvals?.map(a => ({
                userId: typeof a.user === 'object' ? a.user._id : a.user,
                version: a.version,
                createdAt: a.createdAt
              })),
              // Show which approvals match current user and version
              matchingApprovals: requirement.approvals?.filter(a => {
                const approvalUserId = typeof a.user === 'object' ? a.user._id : a.user;
                return approvalUserId === currentUserId;
              }).map(a => ({
                userId: typeof a.user === 'object' ? a.user._id : a.user,
                version: a.version,
                matchesCurrentVersion: a.version === requirement.currentVersion,
                createdAt: a.createdAt
              }))
            });
          }

          if (userActiveTasks.length > 0 || needsApproval) {
            console.log('Found active work for user in requirement:', {
              requirementId: requirement._id,
              userActiveTasks: userActiveTasks.length,
              needsApproval,
              isApprover,
              hasApproved
            });
            hasActiveTasks = true;
            break;
          }
        }
      }

      // Update the navigation state optimistically
      setProjectsWithTasks(prev => {
        const filtered = prev.filter(p => p._id !== projectId);
        if (hasActiveTasks) {
          // Add project to active tasks (with full task data)
          const projectWithTaskData = {
            ...project,
            features: projectFeatures.map(feature => {
              const featureRequirements = projectRequirements.filter(req => {
                const reqFeatureId = typeof req.feature === 'object' ? req.feature._id : req.feature;
                return reqFeatureId === feature._id;
              });
              return {
                ...feature,
                requirements: featureRequirements.filter(req => {
                  const currentVersion = req.versions[req.versions.length - 1];
                  const userActiveTasks = currentVersion.tasks?.filter(task => {
                    const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                    return taskUserId === currentUserId && task.status === 'pending';
                  }) || [];

                  // Check for pending approvals
                  const isApprover = req.approvers && req.approvers.some(approver => {
                    const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
                    return approverId === currentUserId;
                  });

                  const hasApproved = req.approvals && req.approvals.some(approval => {
                    const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
                    return approvalUserId === currentUserId && approval.version === req.currentVersion;
                  });

                  const needsApproval = isApprover && !hasApproved;

                  return userActiveTasks.length > 0 || needsApproval;
                }).map(req => ({
                  ...req,
                  activeTasks: req.versions[req.versions.length - 1].tasks?.filter(task => {
                    const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                    return taskUserId === currentUserId && task.status === 'pending';
                  }) || [],
                  needsApproval: (() => {
                    const isApprover = req.approvers && req.approvers.some(approver => {
                      const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
                      return approverId === currentUserId;
                    });
                    const hasApproved = req.approvals && req.approvals.some(approval => {
                      const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
                      return approvalUserId === currentUserId && approval.version === req.currentVersion;
                    });
                    return isApprover && !hasApproved;
                  })()
                }))
              };
            }).filter(feature => feature.requirements.length > 0),
            topLevelRequirements: projectRequirements.filter(req => !req.feature).filter(req => {
              // Check for active tasks across ALL versions
              let userActiveTasks = [];
              req.versions.forEach(version => {
                if (version.tasks) {
                  const versionTasks = version.tasks.filter(task => {
                    const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                    return taskUserId === currentUserId && task.status === 'pending';
                  });
                  userActiveTasks = userActiveTasks.concat(versionTasks);
                }
              });

              // Check for pending approvals
              const isApprover = req.approvers && req.approvers.some(approver => {
                const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
                return approverId === currentUserId;
              });

              const hasApproved = req.approvals && req.approvals.some(approval => {
                const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
                return approvalUserId === currentUserId && approval.version === req.currentVersion;
              });

              const needsApproval = isApprover && !hasApproved;

              return userActiveTasks.length > 0 || needsApproval;
            }).map(req => {
              // Collect active tasks from ALL versions
              let allActiveTasks = [];
              req.versions.forEach(version => {
                if (version.tasks) {
                  const versionTasks = version.tasks.filter(task => {
                    const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                    return taskUserId === currentUserId && task.status === 'pending';
                  });
                  allActiveTasks = allActiveTasks.concat(versionTasks);
                }
              });

              // Check for pending approvals
              const isApprover = req.approvers && req.approvers.some(approver => {
                const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
                return approverId === currentUserId;
              });

              const hasApproved = req.approvals && req.approvals.some(approval => {
                const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
                return approvalUserId === currentUserId && approval.version === req.currentVersion;
              });

              const needsApproval = isApprover && !hasApproved;

              return {
                ...req,
                activeTasks: allActiveTasks,
                needsApproval: needsApproval
              };
            })
          };
          return [...filtered, projectWithTaskData];
        }
        return filtered;
      });

      setMemberProjects(prev => {
        const filtered = prev.filter(p => p._id !== projectId);
        if (!hasActiveTasks) {
          // Check if user is still a member
          const isUserMember = project.members.some(member => {
            const memberId = typeof member.user === 'object' ? member.user._id : member.user;
            return memberId === currentUserId;
          });
          if (isUserMember) {
            // Create member project with full hierarchy (features and requirements)
            const memberProjectWithHierarchy = {
              ...project,
              features: projectFeatures.map(feature => {
                const featureRequirements = projectRequirements.filter(req => {
                  const reqFeatureId = typeof req.feature === 'object' ? req.feature._id : req.feature;
                  return reqFeatureId === feature._id;
                });
                return {
                  ...feature,
                  requirements: featureRequirements
                };
              }),
              requirements: projectRequirements // All requirements for top-level access
            };
            return [...filtered, memberProjectWithHierarchy];
          }
        }
        return filtered;
      });

      console.log('Navigation updated optimistically for project:', project.name, 'hasActiveTasks:', hasActiveTasks);
      console.log('Active tasks check details:', {
        projectId,
        currentUserId,
        hasActiveTasks,
        taskStatus,
        projectFeatures: projectFeatures.length,
        projectRequirements: projectRequirements.length
      });
    } catch (error) {
      console.error('Error updating navigation optimistically:', error);
      // Fallback to full refresh on error
      fetchNavigationData();
    }
  };

  const updateFeatureTitleInNavigation = (projectId, featureId, newTitle) => {
    console.log('Updating feature title in navigation:', { projectId, featureId, newTitle });

    // Update feature title in projectsWithTasks
    setProjectsWithTasks(prev => prev.map(project => {
      if (project._id === projectId) {
        return {
          ...project,
          features: project.features.map(feature => {
            if (feature._id === featureId) {
              return {
                ...feature,
                versions: feature.versions.map((version, index) => {
                  // Update the latest version (last in array) with new title
                  if (index === feature.versions.length - 1) {
                    return { ...version, title: newTitle };
                  }
                  return version;
                })
              };
            }
            return feature;
          })
        };
      }
      return project;
    }));

    console.log('Feature title updated in navigation for feature:', featureId, 'new title:', newTitle);
  };

  const updateRequirementTitleInNavigation = (projectId, featureId, requirementId, newTitle) => {
    console.log('Updating requirement title in navigation:', { projectId, featureId, requirementId, newTitle });

    // Update requirement title in projectsWithTasks
    setProjectsWithTasks(prev => prev.map(project => {
      if (project._id === projectId) {
        return {
          ...project,
          features: project.features.map(feature => {
            if (feature._id === featureId) {
              return {
                ...feature,
                requirements: feature.requirements.map(requirement => {
                  if (requirement._id === requirementId) {
                    return {
                      ...requirement,
                      versions: requirement.versions.map((version, index) => {
                        // Update the latest version (last in array) with new title
                        if (index === requirement.versions.length - 1) {
                          return { ...version, title: newTitle };
                        }
                        return version;
                      })
                    };
                  }
                  return requirement;
                })
              };
            }
            return feature;
          })
        };
      }
      return project;
    }));

    console.log('Requirement title updated in navigation for requirement:', requirementId, 'new title:', newTitle);
  };

  const fetchNavigationData = async () => {
    try {
      setLoading(true);

      // Get the current user ID (handle both id and _id)
      const currentUserId = user.id || user._id;

      if (!currentUserId) {
        console.error('No user ID found in user object:', user);
        return;
      }

      // Fetch all projects, features, and requirements
      const [projectsRes, featuresRes, requirementsRes] = await Promise.all([
        axios.get('/api/projects'),
        axios.get('/api/features'),
        axios.get('/api/requirements?navigation=true')
      ]);

      const allProjects = projectsRes.data;
      const allFeatures = featuresRes.data;
      const allRequirements = requirementsRes.data;

      console.log('Navigation data fetched:', {
        projects: allProjects.length,
        features: allFeatures.length,
        requirements: allRequirements.length,
        userId: currentUserId,
        userObject: user
      });



      // Build project hierarchy with user's active tasks
      const projectsWithActiveTasks = [];
      const userMemberProjects = [];
      const otherProjects = [];

      allProjects.forEach(project => {
        const isUserMember = project.members.some(member => {
          // Handle both populated and non-populated user fields
          const memberId = typeof member.user === 'object' ? member.user._id : member.user;
          return memberId === currentUserId;
        });



        // Get features for this project (handle populated project field)
        const projectFeatures = allFeatures.filter(feature => {
          const featureProjectId = typeof feature.project === 'object' ? feature.project._id : feature.project;
          return featureProjectId === project._id;
        });

        // Get requirements for this project (handle populated project field)
        const projectRequirements = allRequirements.filter(req => {
          const reqProjectId = typeof req.project === 'object' ? req.project._id : req.project;
          return reqProjectId === project._id;
        });



        // Check for user's active tasks in this project
        let hasActiveTasks = false;
        const projectWithTasks = {
          ...project,
          features: projectFeatures.map(feature => {
            const featureRequirements = projectRequirements.filter(req => {
              const reqFeatureId = typeof req.feature === 'object' ? req.feature._id : req.feature;
              return reqFeatureId === feature._id;
            });

            const featureWithTasks = {
              ...feature,
              requirements: featureRequirements.map(requirement => {
                // Check for active tasks across ALL versions
                let allActiveTasks = [];
                requirement.versions.forEach(version => {
                  if (version.tasks) {
                    const versionTasks = version.tasks.filter(task => {
                      // Handle both populated and non-populated user fields
                      const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                      const isUserTask = taskUserId === currentUserId;
                      const isPending = task.status === 'pending';
                      return isUserTask && isPending;
                    });
                    allActiveTasks = allActiveTasks.concat(versionTasks);
                  }
                });

                // Check for pending approvals
                const isApprover = requirement.approvers && requirement.approvers.some(approver => {
                  const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
                  return approverId === currentUserId;
                });

                const hasApproved = requirement.approvals && requirement.approvals.some(approval => {
                  const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
                  return approvalUserId === currentUserId && approval.version === requirement.currentVersion;
                });

                const needsApproval = isApprover && !hasApproved;

                if (allActiveTasks.length > 0 || needsApproval) {
                  hasActiveTasks = true;
                }

                return {
                  ...requirement,
                  activeTasks: allActiveTasks,
                  needsApproval: needsApproval,
                  isApprover: isApprover,
                  hasApproved: hasApproved
                };
              }).filter(req => req.activeTasks.length > 0 || req.needsApproval)
            };

            return featureWithTasks;
          }).filter(feature => feature.requirements.length > 0),

          // Also check top-level requirements (not under features) - ALL versions
          topLevelRequirements: projectRequirements.filter(req => !req.feature).map(requirement => {
            // Check for active tasks across ALL versions
            let allActiveTasks = [];
            requirement.versions.forEach(version => {
              if (version.tasks) {
                const versionTasks = version.tasks.filter(task => {
                  // Handle both populated and non-populated user fields
                  const taskUserId = typeof task.user === 'object' ? task.user._id : task.user;
                  const isUserTask = taskUserId === currentUserId;
                  const isPending = task.status === 'pending';
                  return isUserTask && isPending;
                });
                allActiveTasks = allActiveTasks.concat(versionTasks);
              }
            });

            // Check for pending approvals
            const isApprover = requirement.approvers && requirement.approvers.some(approver => {
              const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
              return approverId === currentUserId;
            });

            const hasApproved = requirement.approvals && requirement.approvals.some(approval => {
              const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
              return approvalUserId === currentUserId && approval.version === requirement.currentVersion;
            });

            const needsApproval = isApprover && !hasApproved;

            if (allActiveTasks.length > 0 || needsApproval) {
              hasActiveTasks = true;
            }

            return {
              ...requirement,
              activeTasks: allActiveTasks,
              needsApproval: needsApproval,
              isApprover: isApprover,
              hasApproved: hasApproved
            };
          }).filter(req => req.activeTasks.length > 0 || req.needsApproval)
        };

        if (hasActiveTasks) {
          projectsWithActiveTasks.push(projectWithTasks);
        } else if (isUserMember) {
          // Create member project with full hierarchy (features and requirements)
          const memberProjectWithHierarchy = {
            ...project,
            features: projectFeatures.map(feature => {
              const featureRequirements = projectRequirements.filter(req => {
                const reqFeatureId = typeof req.feature === 'object' ? req.feature._id : req.feature;
                return reqFeatureId === feature._id;
              });
              return {
                ...feature,
                requirements: featureRequirements
              };
            }),
            requirements: projectRequirements // All requirements for top-level access
          };
          userMemberProjects.push(memberProjectWithHierarchy);
        } else {
          otherProjects.push(project);
        }
      });

      console.log('Final categorization:', {
        projectsWithActiveTasks: projectsWithActiveTasks.length,
        userMemberProjects: userMemberProjects.length,
        otherProjects: otherProjects.length
      });

      setProjectsWithTasks(projectsWithActiveTasks);
      setMemberProjects(userMemberProjects);
      setAllOtherProjects(otherProjects);
    } catch (error) {
      console.error('Error fetching navigation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = (key) => {
    setExpanded(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const renderTaskProject = (project) => {
    const projectKey = `task-project-${project._id}`;

    return (
      <React.Fragment key={project._id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => handleToggle(projectKey)}
            sx={{ pl: 6 }}
          >
            <ListItemIcon>
              {expanded[projectKey] ? <ExpandMore /> : <ChevronRightIcon />}
            </ListItemIcon>
            <ListItemIcon>
              <FolderIcon sx={{ color: '#d32f2f' }} />
            </ListItemIcon>
            <Tooltip title={project.name} arrow placement="right">
              <ListItemText
                primary={project.name}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/project/${project._id}`);
                }}
                sx={{
                  cursor: 'pointer',
                  '& .MuiListItemText-primary': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '150px'
                  }
                }}
              />
            </Tooltip>
          </ListItemButton>
        </ListItem>
        <Collapse in={expanded[projectKey]} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {/* Render features with requirements that have active tasks */}
            {project.features.map(feature => renderTaskFeature(feature, project._id))}

            {/* Render top-level requirements with active tasks */}
            {project.topLevelRequirements.map(requirement => renderTaskRequirement(requirement, project._id, null))}
          </List>
        </Collapse>
      </React.Fragment>
    );
  };

  const renderTaskFeature = (feature, projectId) => {
    const featureKey = `task-feature-${feature._id}`;
    const currentVersion = feature.versions[feature.versions.length - 1];
    const hasRequirements = feature.requirements && feature.requirements.length > 0;

    return (
      <React.Fragment key={feature._id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={hasRequirements ? () => handleToggle(featureKey) : undefined}
            sx={{ pl: 8 }}
          >
            {hasRequirements && (
              <ListItemIcon>
                {expanded[featureKey] ? <ExpandMore /> : <ChevronRightIcon />}
              </ListItemIcon>
            )}
            <ListItemIcon>
              <FeatureIcon sx={{ color: '#d32f2f' }} />
            </ListItemIcon>
            <Tooltip title={currentVersion.title} arrow placement="right">
              <ListItemText
                primary={currentVersion.title}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/feature/${feature._id}`);
                }}
                sx={{
                  cursor: 'pointer',
                  '& .MuiListItemText-primary': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '150px'
                  }
                }}
              />
            </Tooltip>
          </ListItemButton>
        </ListItem>
        {hasRequirements && (
          <Collapse in={expanded[featureKey]} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {feature.requirements.map(requirement => renderTaskRequirement(requirement, projectId, feature._id))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const renderTaskRequirement = (requirement, projectId, featureId) => {
    const requirementKey = `task-requirement-${requirement._id}`;
    const currentVersion = requirement.versions[requirement.versions.length - 1];
    const paddingLeft = featureId ? 10 : 8;

    return (
      <React.Fragment key={requirement._id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => handleToggle(requirementKey)}
            sx={{ pl: paddingLeft }}
          >
            <ListItemIcon>
              {expanded[requirementKey] ? <ExpandMore /> : <ChevronRightIcon />}
            </ListItemIcon>
            <ListItemIcon>
              <AssignmentIcon sx={{ color: '#d32f2f' }} />
            </ListItemIcon>
            <Tooltip title={currentVersion.title} arrow placement="right">
              <ListItemText
                primary={currentVersion.title}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/requirement/${requirement._id}`);
                }}
                sx={{
                  cursor: 'pointer',
                  '& .MuiListItemText-primary': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '150px'
                  }
                }}
              />
            </Tooltip>
          </ListItemButton>
        </ListItem>
        <Collapse in={expanded[requirementKey]} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {requirement.activeTasks.map(task => (
              <ListItem
                key={task._id}
                disablePadding
                onClick={() => navigate(`/requirement/${requirement._id}`)}
                sx={{ cursor: 'pointer' }}
              >
                <ListItemButton sx={{ pl: paddingLeft + 2 }}>
                  <ListItemIcon>
                    <TaskIcon sx={{ color: '#d32f2f' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={task.text}
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
            {requirement.needsApproval && (
              <ListItem
                key={`approval-${requirement._id}`}
                disablePadding
                onClick={() => {
                  // Check if requirement version has changed since user was added as approver
                  const approverInfo = requirement.approvers?.find(approver => {
                    const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
                    const currentUserId = user.id || user._id;
                    return approverId === currentUserId;
                  });

                  if (approverInfo && approverInfo.addedInVersion < requirement.currentVersion) {
                    // Version has changed, open with version popup
                    navigate(`/requirement/${requirement._id}?showVersionHistory=true`);
                  } else {
                    // Same version, just navigate to requirement
                    navigate(`/requirement/${requirement._id}`);
                  }
                }}
                sx={{ cursor: 'pointer' }}
              >
                <ListItemButton sx={{ pl: paddingLeft + 2 }}>
                  <ListItemIcon>
                    <ApprovalIcon sx={{ color: '#7b1fa2' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary="approval required"
                    primaryTypographyProps={{ variant: 'body2', fontStyle: 'italic' }}
                  />
                </ListItemButton>
              </ListItem>
            )}
          </List>
        </Collapse>
      </React.Fragment>
    );
  };

  const renderMemberProject = (project) => {
    const projectKey = `member-project-${project._id}`;

    return (
      <React.Fragment key={project._id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => handleToggle(projectKey)}
            sx={{ pl: 6 }}
          >
            <ListItemIcon>
              {expanded[projectKey] ? <ExpandMore /> : <ChevronRightIcon />}
            </ListItemIcon>
            <ListItemIcon>
              <FolderIcon sx={{ color: '#1976d2' }} />
            </ListItemIcon>
            <Tooltip title={project.name} arrow placement="right">
              <ListItemText
                primary={project.name}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/project/${project._id}`);
                }}
                sx={{
                  cursor: 'pointer',
                  '& .MuiListItemText-primary': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '150px'
                  }
                }}
              />
            </Tooltip>
          </ListItemButton>
        </ListItem>
        <Collapse in={expanded[projectKey]} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {/* Render features */}
            {project.features?.map(feature => renderMemberFeature(feature, project._id))}

            {/* Render top-level requirements */}
            {project.requirements?.filter(req => !req.feature).map(requirement =>
              renderMemberRequirement(requirement, project._id, null)
            )}
          </List>
        </Collapse>
      </React.Fragment>
    );
  };

  const renderMemberFeature = (feature, projectId) => {
    const featureKey = `member-feature-${feature._id}`;
    const currentVersion = feature.versions[feature.versions.length - 1];
    const hasRequirements = feature.requirements && feature.requirements.length > 0;

    return (
      <React.Fragment key={feature._id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={hasRequirements ? () => handleToggle(featureKey) : undefined}
            sx={{ pl: 8 }}
          >
            {hasRequirements && (
              <ListItemIcon>
                {expanded[featureKey] ? <ExpandMore /> : <ChevronRightIcon />}
              </ListItemIcon>
            )}
            <ListItemIcon>
              <FeatureIcon sx={{ color: '#1976d2' }} />
            </ListItemIcon>
            <Tooltip title={currentVersion.title} arrow placement="right">
              <ListItemText
                primary={currentVersion.title}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/feature/${feature._id}`);
                }}
                sx={{
                  cursor: 'pointer',
                  '& .MuiListItemText-primary': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '150px'
                  }
                }}
              />
            </Tooltip>
          </ListItemButton>
        </ListItem>
        {hasRequirements && (
          <Collapse in={expanded[featureKey]} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {feature.requirements.map(requirement =>
                renderMemberRequirement(requirement, projectId, feature._id)
              )}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const renderMemberRequirement = (requirement, projectId, featureId) => {
    const currentVersion = requirement.versions[requirement.versions.length - 1];
    // Add extra padding to align clipboard icon to the right of feature Extension icon above
    const paddingLeft = featureId ? 18 : 16;

    return (
      <ListItem
        key={requirement._id}
        disablePadding
        onClick={() => navigate(`/requirement/${requirement._id}`)}
        sx={{ cursor: 'pointer' }}
      >
        <ListItemButton sx={{ pl: paddingLeft }}>
          <ListItemIcon>
            <AssignmentIcon sx={{ color: '#1976d2' }} />
          </ListItemIcon>
          <Tooltip title={currentVersion.title} arrow placement="right">
            <ListItemText
              primary={currentVersion.title}
              primaryTypographyProps={{
                variant: 'body2',
                sx: {
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '150px'
                }
              }}
            />
          </Tooltip>
        </ListItemButton>
      </ListItem>
    );
  };

  const renderSimpleProject = (project, paddingLeft = 2, iconColor = '#1976d2') => (
    <ListItem
      key={project._id}
      disablePadding
      onClick={() => navigate(`/project/${project._id}`)}
      sx={{ cursor: 'pointer' }}
    >
      <ListItemButton sx={{ pl: paddingLeft }}>
        <ListItemIcon>
          <FolderIcon sx={{ color: iconColor }} />
        </ListItemIcon>
        <Tooltip title={project.name} arrow placement="right">
          <ListItemText
            primary={project.name}
            sx={{
              '& .MuiListItemText-primary': {
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '150px'
              }
            }}
          />
        </Tooltip>
      </ListItemButton>
    </ListItem>
  );

  if (loading) {
    return (
      <Box sx={{ position: 'relative' }}>
        <Drawer
          variant="permanent"
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
              top: 56, // Account for compact app bar height
              height: 'calc(100% - 56px)',
              transition: isResizing ? 'none' : 'width 0.2s ease'
            },
          }}
        >
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
            <CircularProgress />
          </Box>
        </Drawer>

        {/* Resize Handle */}
        {!isMobile && (
          <Box
            onMouseDown={handleMouseDown}
            onDoubleClick={handleDoubleClick}
            sx={{
              position: 'absolute',
              top: 56,
              right: 0,
              width: 4,
              height: 'calc(100vh - 56px)',
              cursor: 'col-resize',
              backgroundColor: 'transparent',
              borderRight: '1px solid transparent',
              transition: 'border-color 0.2s ease',
              zIndex: 1300,
              '&:hover': {
                borderRight: '1px solid',
                borderColor: 'primary.main',
                backgroundColor: 'rgba(25, 118, 210, 0.1)'
              }
            }}
          />
        )}
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            top: 56, // Account for compact app bar height
            height: 'calc(100% - 56px)',
            transition: isResizing ? 'none' : 'width 0.2s ease'
          },
        }}
      >
        <List>
        {/* My Projects Section */}
        <ListItem disablePadding>
          <ListItemButton onClick={() => handleToggle('myProjects')}>
            <ListItemIcon>
              {expanded.myProjects ? <ExpandMore /> : <ChevronRightIcon />}
            </ListItemIcon>
            <ListItemIcon>
              <PersonIcon sx={{ color: '#00796b' }} />
            </ListItemIcon>
            <ListItemText primary="My Projects" />
          </ListItemButton>
        </ListItem>
        <Collapse in={expanded.myProjects} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {/* Open Tasks Subsection */}
            {projectsWithTasks.length > 0 && (
              <>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => handleToggle('openTasks')} sx={{ pl: 4 }}>
                    <ListItemIcon>
                      {expanded.openTasks ? <ExpandMore /> : <ChevronRightIcon />}
                    </ListItemIcon>
                    <ListItemIcon>
                      <TaskIcon sx={{ color: '#d32f2f' }} />
                    </ListItemIcon>
                    <ListItemText primary="Open Tasks" />
                  </ListItemButton>
                </ListItem>
                <Collapse in={expanded.openTasks} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {projectsWithTasks.map(project => renderTaskProject(project))}
                  </List>
                </Collapse>
              </>
            )}

            {/* Member of Subsection */}
            {memberProjects.length > 0 && (
              <>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => handleToggle('memberOf')} sx={{ pl: 4 }}>
                    <ListItemIcon>
                      {expanded.memberOf ? <ExpandMore /> : <ChevronRightIcon />}
                    </ListItemIcon>
                    <ListItemIcon>
                      <GroupIcon sx={{ color: '#1976d2' }} />
                    </ListItemIcon>
                    <ListItemText primary="Member of" />
                  </ListItemButton>
                </ListItem>
                <Collapse in={expanded.memberOf} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {memberProjects.map(project => renderMemberProject(project))}
                  </List>
                </Collapse>
              </>
            )}
          </List>
        </Collapse>

        {/* All Other Projects Section */}
        <ListItem disablePadding>
          <ListItemButton onClick={() => handleToggle('allProjects')}>
            <ListItemIcon>
              {expanded.allProjects ? <ExpandMore /> : <ChevronRightIcon />}
            </ListItemIcon>
            <ListItemIcon>
              <PublicIcon sx={{ color: '#673ab7' }} />
            </ListItemIcon>
            <ListItemText primary="All Other Projects" />
          </ListItemButton>
        </ListItem>
        <Collapse in={expanded.allProjects} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {allOtherProjects.map(project => renderSimpleProject(project, 2, '#673ab7'))}
          </List>
        </Collapse>
        </List>
      </Drawer>

      {/* Resize Handle */}
      {!isMobile && (
        <>
          <Box
            onMouseDown={handleMouseDown}
            onDoubleClick={handleDoubleClick}
            onContextMenu={handleContextMenu}
            sx={{
              position: 'absolute',
              top: 56,
              right: 0,
              width: 4,
              height: 'calc(100vh - 56px)',
              cursor: 'col-resize',
              backgroundColor: 'transparent',
              borderRight: '1px solid transparent',
              transition: 'border-color 0.2s ease',
              zIndex: 1300,
              '&:hover': {
                borderRight: '1px solid',
                borderColor: 'primary.main',
                backgroundColor: 'rgba(25, 118, 210, 0.1)'
              }
            }}
          />

          {/* Context Menu */}
          <Menu
            open={contextMenu !== null}
            onClose={handleContextMenuClose}
            anchorReference="anchorPosition"
            anchorPosition={
              contextMenu !== null
                ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
                : undefined
            }
          >
            <MenuItem onClick={handleResetWidth}>
              Reset to Default Width
            </MenuItem>
          </Menu>
        </>
      )}
    </Box>
  );
};

export default NavigationSidebar;
