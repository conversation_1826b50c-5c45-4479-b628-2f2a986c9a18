import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  LocalOffer as TagIcon,
  Label as LabelIcon,
  Assignment as RequirementIcon,
  Folder as FeatureIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import Breadcrumbs from '../common/Breadcrumbs';

const ReleaseNotesGenerator = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const { axios: authAxios, isAuthenticated, loading: authLoading } = useAuth();
  
  // State management
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [generating, setGenerating] = useState(false);
  
  // Filter state
  const [filters, setFilters] = useState({
    tags: [],
    labels: [],
    states: [],
    includeFeatures: true,
    includeRequirements: true,
    groupByFeature: true
  });
  
  // Available options
  const [availableTags, setAvailableTags] = useState([]);
  const [availableLabels, setAvailableLabels] = useState([]);
  const [availableStates] = useState([
    // Feature states
    'NEW', 'OPEN', 'CLOSED',
    // Requirement states
    'New', 'Being Drafted', 'Requiring Approval', 'Ready for Dev', 
    'Being Built', 'Ready for Test', 'Under Test', 'Testing Satisfactory', 
    'Shipped/Deployed to Customer'
  ]);
  
  // Release notes data
  const [releaseData, setReleaseData] = useState(null);
  const [releaseNotes, setReleaseNotes] = useState('');
  const [releaseTitle, setReleaseTitle] = useState('');
  const [savedReleaseNotes, setSavedReleaseNotes] = useState([]);

  useEffect(() => {
    if (authLoading) return;
    
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    fetchProjectData();
  }, [projectId, authAxios, isAuthenticated, authLoading, navigate]);

  const fetchProjectData = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Fetch project details
      const projectRes = await authAxios.get(`/api/projects/${projectId}`);
      setProject(projectRes.data);
      
      // Fetch available tags and labels for this project
      const releaseDataRes = await authAxios.get(`/api/release-notes/projects/${projectId}/release-data`);
      setAvailableTags(releaseDataRes.data.tags);
      setAvailableLabels(releaseDataRes.data.labels);
      
      // Fetch saved release notes
      const savedNotesRes = await authAxios.get(`/api/release-notes/projects/${projectId}/release-notes`);
      setSavedReleaseNotes(savedNotesRes.data);
      
    } catch (err) {
      console.error('Error fetching project data:', err);
      setError(err.response?.data?.message || 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const addFilterItem = (filterType, item) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: [...prev[filterType], item]
    }));
  };

  const removeFilterItem = (filterType, item) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: prev[filterType].filter(i => i !== item)
    }));
  };

  const generateReleaseNotes = async () => {
    try {
      setGenerating(true);
      setError('');
      
      const response = await authAxios.post(`/api/release-notes/projects/${projectId}/release-notes`, {
        filters,
        title: releaseTitle || `Release Notes - ${new Date().toLocaleDateString()}`
      });
      
      setReleaseData(response.data.data);
      setReleaseNotes(response.data.releaseNotes);
      
    } catch (err) {
      console.error('Error generating release notes:', err);
      setError(err.response?.data?.message || 'Failed to generate release notes');
    } finally {
      setGenerating(false);
    }
  };

  const saveReleaseNotes = async () => {
    try {
      setError('');
      
      const response = await authAxios.post(`/api/release-notes/projects/${projectId}/release-notes`, {
        filters,
        title: releaseTitle || `Release Notes - ${new Date().toLocaleDateString()}`,
        content: releaseNotes,
        save: true
      });
      
      // Refresh saved release notes list
      await fetchProjectData();
      
      setError(''); // Clear any previous errors
      // Could add a success message here
      
    } catch (err) {
      console.error('Error saving release notes:', err);
      setError(err.response?.data?.message || 'Failed to save release notes');
    }
  };

  const downloadReleaseNotes = (format = 'txt') => {
    const content = releaseNotes;
    const filename = `${releaseTitle || 'release-notes'}.${format}`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (authLoading || loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !project) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      <Breadcrumbs 
        items={[
          { label: 'Projects', path: '/projects' },
          { label: project?.name || 'Project', path: `/project/${projectId}` },
          { label: 'Release Notes', path: `/project/${projectId}/release-notes` }
        ]} 
      />

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <HistoryIcon sx={{ mr: 2, color: 'primary.main' }} />
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Release Notes Generator
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Filter Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 'fit-content' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <FilterIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Filters</Typography>
            </Box>
            
            {/* Release Title */}
            <TextField
              fullWidth
              label="Release Title"
              value={releaseTitle}
              onChange={(e) => setReleaseTitle(e.target.value)}
              placeholder="e.g., Version 1.0.0, Sprint 15 Release"
              sx={{ mb: 2 }}
            />

            {/* Content Type Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Include Content
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.includeFeatures}
                  onChange={(e) => handleFilterChange('includeFeatures', e.target.checked)}
                />
              }
              label="Features"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.includeRequirements}
                  onChange={(e) => handleFilterChange('includeRequirements', e.target.checked)}
                />
              }
              label="Requirements"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.groupByFeature}
                  onChange={(e) => handleFilterChange('groupByFeature', e.target.checked)}
                />
              }
              label="Group by Feature"
            />

            <Divider sx={{ my: 2 }} />

            {/* Tag Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              <TagIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
              Release Tags
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Tags</InputLabel>
              <Select
                multiple
                value={filters.tags}
                onChange={(e) => handleFilterChange('tags', e.target.value)}
                label="Select Tags"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableTags.map((tag) => (
                  <MenuItem key={tag} value={tag}>
                    {tag}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Label Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              <LabelIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
              Labels
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Labels</InputLabel>
              <Select
                multiple
                value={filters.labels}
                onChange={(e) => handleFilterChange('labels', e.target.value)}
                label="Select Labels"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((label) => (
                      <Chip key={label._id} label={label.name} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableLabels.map((label) => (
                  <MenuItem key={label._id} value={label}>
                    {label.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* State Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              States
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select States</InputLabel>
              <Select
                multiple
                value={filters.states}
                onChange={(e) => handleFilterChange('states', e.target.value)}
                label="Select States"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableStates.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Divider sx={{ my: 2 }} />

            {/* Generate Button */}
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={generateReleaseNotes}
              disabled={generating || (!filters.includeFeatures && !filters.includeRequirements)}
              startIcon={generating ? <CircularProgress size={20} /> : <PreviewIcon />}
              sx={{ mb: 2 }}
            >
              {generating ? 'Generating...' : 'Generate Release Notes'}
            </Button>
          </Paper>
        </Grid>

        {/* Main Content Area */}
        <Grid item xs={12} md={8}>
          {/* Release Notes Content */}
          <Paper sx={{ p: 3, minHeight: 400, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Generated Release Notes
            </Typography>
            {releaseNotes ? (
              <Box>
                <TextField
                  fullWidth
                  multiline
                  rows={20}
                  value={releaseNotes}
                  onChange={(e) => setReleaseNotes(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={saveReleaseNotes}
                    disabled={!releaseTitle.trim()}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={() => downloadReleaseNotes('txt')}
                  >
                    Download
                  </Button>
                </Box>
              </Box>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                Configure your filters and click "Generate Release Notes" to create release notes.
              </Typography>
            )}
          </Paper>

          {/* Saved Release Notes History */}
          {savedReleaseNotes.length > 0 && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Saved Release Notes
              </Typography>
              <List>
                {savedReleaseNotes.map((notes) => (
                  <ListItem
                    key={notes._id}
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                  >
                    <ListItemText
                      primary={notes.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Created: {new Date(notes.createdAt).toLocaleString()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Filters: {notes.filters.tags?.length || 0} tags, {notes.filters.labels?.length || 0} labels, {notes.filters.states?.length || 0} states
                          </Typography>
                        </Box>
                      }
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Release Notes">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setReleaseNotes(notes.content);
                            setReleaseTitle(notes.title);
                            setFilters(notes.filters);
                          }}
                        >
                          <PreviewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download">
                        <IconButton
                          size="small"
                          onClick={() => {
                            const blob = new Blob([notes.content], { type: 'text/plain' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${notes.title}.txt`;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                          }}
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReleaseNotesGenerator;
