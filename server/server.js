require('dotenv').config();
const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const socketio = require('socket.io');
const http = require('http');
const jwt = require('jsonwebtoken');
const User = require('./models/User');

// Initialize express app
const app = express();
const server = http.createServer(app);
const io = socketio(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
// Increase payload limit to handle images (10MB limit)
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// Debug logging middleware
app.use((req, res, next) => {
  console.log('Incoming request:', {
    method: req.method,
    url: req.url,
    path: req.path,
    params: req.params,
    query: req.query,
    body: req.body
  });
  next();
});

// Load routes
console.log('Loading routes...');
const requirementsRouter = require('./routes/requirements');
const featuresRouter = require('./routes/features');
const projectsRouter = require('./routes/projects');
const usersRouter = require('./routes/users');
const authRouter = require('./routes/auth');
const labelsRouter = require('./routes/labels');
const groupsRouter = require('./routes/groups');
const adminRouter = require('./routes/admin');
const externalRouter = require('./routes/external');
const releaseNotesRouter = require('./routes/releaseNotes');

// Debug route to check if server is running
app.get('/api/health', (req, res) => {
  console.log('Health check route hit');
  const routes = [];
  app._router.stack.forEach(r => {
    if (r.route) {
      routes.push({
        path: r.route.path,
        methods: Object.keys(r.route.methods)
      });
    } else if (r.name === 'router') {
      console.log('Router mounted at:', r.regexp);
      r.handle.stack.forEach(handler => {
        if (handler.route) {
          routes.push({
            path: `${r.regexp}${handler.route.path}`,
            methods: Object.keys(handler.route.methods)
          });
        }
      });
    }
  });
  console.log('Available routes:', routes);
  res.json({
    status: 'ok',
    routes,
    timestamp: new Date().toISOString()
  });
});

// Register routes
console.log('Registering routes...');
app.use('/api/requirements', requirementsRouter);
app.use('/api/features', featuresRouter);
app.use('/api/projects', projectsRouter);
app.use('/api/users', usersRouter);
app.use('/api/auth', authRouter);
app.use('/api/labels', labelsRouter);
app.use('/api/groups', groupsRouter);
app.use('/api/admin', adminRouter);
app.use('/api/external', externalRouter);
app.use('/api/release-notes', releaseNotesRouter);

// Socket.io middleware for authentication
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error('Authentication error'));
  }

  try {
    const decoded = jwt.verify(token, 'your_jwt_secret');  // Using hardcoded secret to force logout on server restart
    socket.userId = decoded.userId;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.userId);

  socket.on('joinRequirement', (requirementId) => {
    socket.join(requirementId);
  });

  socket.on('leaveRequirement', (requirementId) => {
    socket.leave(requirementId);
  });

  socket.on('newComment', (data) => {
    io.to(data.requirementId).emit('commentAdded', data);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.userId);
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ message: 'Something went wrong!' });
});

// Connect to MongoDB and start server
async function startServer() {
  try {
    // Connect to MongoDB Atlas
    const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://your-atlas-connection-string';
    console.log('Connecting to MongoDB Atlas...');
    console.log('MongoDB URI (masked):', MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 60000, // Increase timeout to 60 seconds
      connectTimeoutMS: 60000,
      socketTimeoutMS: 60000,
      maxPoolSize: 10,
      retryWrites: true,
      w: 'majority'
    });
    console.log('MongoDB Atlas connected successfully');

    // Start server
    const PORT = process.env.PORT || 5000;
    server.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log('Registered routes:');
      app._router.stack.forEach(r => {
        if (r.route && r.route.path) {
          console.log(`${Object.keys(r.route.methods)} ${r.route.path}`);
        } else if (r.name === 'router') {
          console.log('Router mounted at:', r.regexp);
          r.handle.stack.forEach(handler => {
            if (handler.route) {
              console.log(`  ${Object.keys(handler.route.methods)} ${handler.route.path}`);
            }
          });
        }
      });
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();