import React, { useState } from 'react';
import {
  Box,
  Chip,
  TextField,
  Button,
  Typography,
  IconButton,
  Tooltip,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  LocalOffer as TagIcon
} from '@mui/icons-material';
import axios from 'axios';

const TagManager = ({ 
  item, 
  itemType, // 'feature' or 'requirement'
  onUpdate,
  sx = {}
}) => {
  const [newTag, setNewTag] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tagToDelete, setTagToDelete] = useState(null);

  // Get tags from current version
  const currentVersion = item?.versions?.[item.versions.length - 1];
  const releaseTags = currentVersion?.releaseTags || [];

  const handleAddTag = async () => {
    if (!newTag.trim()) return;

    try {
      setLoading(true);
      setError('');
      
      const endpoint = itemType === 'feature' 
        ? `/api/features/${item._id}/tags`
        : `/api/requirements/${item._id}/tags`;
      
      const response = await axios.post(endpoint, {
        tag: newTag.trim()
      });

      if (onUpdate) {
        onUpdate(response.data);
      }

      setNewTag('');
      setIsAdding(false);
    } catch (err) {
      console.error('Error adding tag:', err);
      setError(err.response?.data?.message || 'Failed to add tag');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTag = async (tag) => {
    try {
      setLoading(true);
      setError('');
      
      const endpoint = itemType === 'feature' 
        ? `/api/features/${item._id}/tags/${encodeURIComponent(tag)}`
        : `/api/requirements/${item._id}/tags/${encodeURIComponent(tag)}`;
      
      const response = await axios.delete(endpoint);

      if (onUpdate) {
        onUpdate(response.data);
      }

      setDeleteDialogOpen(false);
      setTagToDelete(null);
    } catch (err) {
      console.error('Error removing tag:', err);
      setError(err.response?.data?.message || 'Failed to remove tag');
    } finally {
      setLoading(false);
    }
  };

  const openDeleteDialog = (tag) => {
    setTagToDelete(tag);
    setDeleteDialogOpen(true);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ ...sx }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <TagIcon sx={{ mr: 1, color: 'text.secondary' }} />
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Release Tags
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Existing Tags */}
      <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
        {releaseTags.map((tagObj, index) => (
          <Tooltip
            key={index}
            title={
              <Box>
                <Typography variant="body2">
                  Added by: {tagObj.addedBy?.firstName} {tagObj.addedBy?.lastName} ({tagObj.addedBy?.username})
                </Typography>
                <Typography variant="body2">
                  Date: {formatDate(tagObj.dateAdded)}
                </Typography>
              </Box>
            }
          >
            <Chip
              label={tagObj.tag}
              color="primary"
              variant="outlined"
              size="small"
              deleteIcon={
                <IconButton size="small" sx={{ '&:hover': { color: 'error.main' } }}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              }
              onDelete={() => openDeleteDialog(tagObj.tag)}
              sx={{
                '& .MuiChip-deleteIcon': {
                  fontSize: '16px'
                }
              }}
            />
          </Tooltip>
        ))}
        
        {/* Add Tag Button */}
        {!isAdding && (
          <Chip
            label="Add Tag"
            icon={<AddIcon />}
            onClick={() => setIsAdding(true)}
            variant="outlined"
            color="primary"
            size="small"
            sx={{
              '&:hover': {
                borderColor: 'primary.main',
                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
              }
            }}
          />
        )}
      </Stack>

      {/* Add Tag Input */}
      {isAdding && (
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mb: 2 }}>
          <TextField
            size="small"
            placeholder="Enter tag name..."
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleAddTag();
              } else if (e.key === 'Escape') {
                setIsAdding(false);
                setNewTag('');
              }
            }}
            autoFocus
            disabled={loading}
            sx={{ minWidth: 200 }}
          />
          <Button
            size="small"
            variant="contained"
            onClick={handleAddTag}
            disabled={loading || !newTag.trim()}
          >
            Add
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={() => {
              setIsAdding(false);
              setNewTag('');
            }}
            disabled={loading}
          >
            Cancel
          </Button>
        </Box>
      )}

      {releaseTags.length === 0 && !isAdding && (
        <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
          No release tags yet. Click "Add Tag" to create the first one.
        </Typography>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Remove Release Tag</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove the tag "{tagToDelete}"?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={() => handleDeleteTag(tagToDelete)} 
            color="error"
            disabled={loading}
          >
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TagManager;
